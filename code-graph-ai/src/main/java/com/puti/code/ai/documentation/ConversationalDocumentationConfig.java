package com.puti.code.ai.documentation;

import com.puti.code.ai.support.TokenSupport;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 多轮对话文档生成配置类
 * 管理对话式文档生成的各种参数
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class ConversationalDocumentationConfig {

    // 对话模式配置
    private static final boolean DEFAULT_ENABLE_CONVERSATIONAL_MODE = true;
    private static final int DEFAULT_MAX_CONVERSATION_ROUNDS = 10;
    private static final int DEFAULT_MEMORY_WINDOW_SIZE = 20;
    private static final boolean DEFAULT_AUTO_CLEANUP_MEMORY = true;
    
    // 层级策略配置
    private static final boolean DEFAULT_LEVEL_1_USE_CONVERSATIONAL = false;  // 第1层通常较简单，使用传统模式
    private static final boolean DEFAULT_LEVEL_2_USE_CONVERSATIONAL = true;   // 第2层推荐使用对话模式
    private static final boolean DEFAULT_LEVEL_3_USE_CONVERSATIONAL = true;   // 第3层推荐使用对话模式
    
    // 内容大小阈值配置
    private static final int DEFAULT_MIN_METHODS_FOR_CONVERSATIONAL = 15;     // 方法数超过此值时使用对话模式
    private static final int DEFAULT_MIN_CONTENT_SIZE_FOR_CONVERSATIONAL = 50000; // 内容大小超过此值时使用对话模式

    /**
     * 是否启用多轮对话模式
     */
    public static boolean isConversationalModeEnabled() {
        return DEFAULT_ENABLE_CONVERSATIONAL_MODE;
    }

    /**
     * 获取最大对话轮数
     */
    public static int getMaxConversationRounds() {
        return DEFAULT_MAX_CONVERSATION_ROUNDS;
    }

    /**
     * 根据层级判断是否应该使用对话模式
     */
    public static boolean shouldUseConversationalForLevel(int level) {
        return switch (level) {
            case 1 -> DEFAULT_LEVEL_1_USE_CONVERSATIONAL;
            case 2 -> DEFAULT_LEVEL_2_USE_CONVERSATIONAL;
            default -> DEFAULT_LEVEL_3_USE_CONVERSATIONAL;
        };
    }

    /**
     * 根据内容复杂度判断是否应该使用对话模式
     */
    public static boolean shouldUseConversationalForContent(int methodCount, int contentSize) {
        return methodCount >= DEFAULT_MIN_METHODS_FOR_CONVERSATIONAL || 
               contentSize >= DEFAULT_MIN_CONTENT_SIZE_FOR_CONVERSATIONAL;
    }

    /**
     * 综合判断是否应该使用对话模式
     */
    public static boolean shouldUseConversationalMode(DocumentationGenerationContext context) {
        if (!isConversationalModeEnabled()) {
            return false;
        }

        // 基于层级的判断
        boolean levelRecommendation = shouldUseConversationalForLevel(context.getLevel());
        
        // 基于内容复杂度的判断
        int methodCount = getMethodCountForLevel(context);
        int contentSize = estimateContentSizeForLevel(context);
        boolean contentRecommendation = shouldUseConversationalForContent(methodCount, contentSize);

        // 综合判断：层级推荐或内容复杂度推荐
        boolean result = levelRecommendation || contentRecommendation;
        
        log.debug("对话模式判断 - 层级: {}, 方法数: {}, 内容大小: {}, 层级推荐: {}, 内容推荐: {}, 最终决定: {}", 
                context.getLevel(), methodCount, contentSize, levelRecommendation, contentRecommendation, result);
        
        return result;
    }

    /**
     * 获取指定层级的方法数
     */
    private static int getMethodCountForLevel(DocumentationGenerationContext context) {
        if (context.getSubgraph() == null) {
            return 0;
        }
        
        return switch (context.getLevel()) {
            case 1 -> context.getSubgraph().getMethodsAtLevel(1).size();
            case 2 -> context.getSubgraph().getMethodsAtLevel(2).size();
            default -> context.getSubgraph().getAllMethods().size();
        };
    }

    /**
     * 估算指定层级的内容大小
     */
    private static int estimateContentSizeForLevel(DocumentationGenerationContext context) {
        if (context.getSubgraph() == null) {
            return 0;
        }
        
        var methods = switch (context.getLevel()) {
            case 1 -> context.getSubgraph().getMethodsAtLevel(1);
            case 2 -> context.getSubgraph().getMethodsAtLevel(2);
            case 3 -> context.getSubgraph().getAllMethods();
            default -> context.getSubgraph().getAllMethods();
        };

        return TokenSupport.estimateContentSize(methods);
    }

    /**
     * 获取建议的批次大小（对话轮数）
     */
    public static int getSuggestedBatchCount(DocumentationGenerationContext context) {
        int methodCount = getMethodCountForLevel(context);
        int contentSize = estimateContentSizeForLevel(context);
        
        // 基于内容复杂度计算建议的批次数
        int batchCount = Math.max(2, Math.min(getMaxConversationRounds(), 
                (methodCount / 8) + (contentSize / 30000)));
        
        log.debug("建议批次数计算 - 方法数: {}, 内容大小: {}, 建议批次数: {}", 
                methodCount, contentSize, batchCount);
        
        return batchCount;
    }

    /**
     * 验证配置的合理性
     */
    public static boolean validateConfig() {
        try {
            if (DEFAULT_MAX_CONVERSATION_ROUNDS <= 0) {
                log.error("最大对话轮数配置无效");
                return false;
            }
            
            if (DEFAULT_MEMORY_WINDOW_SIZE <= 0) {
                log.error("记忆窗口大小配置无效");
                return false;
            }
            
            if (DEFAULT_MIN_METHODS_FOR_CONVERSATIONAL < 0) {
                log.error("最小方法数阈值配置无效");
                return false;
            }
            
            if (DEFAULT_MIN_CONTENT_SIZE_FOR_CONVERSATIONAL < 0) {
                log.error("最小内容大小阈值配置无效");
                return false;
            }
            
            log.info("多轮对话配置验证通过");
            return true;
            
        } catch (Exception e) {
            log.error("验证多轮对话配置时发生错误", e);
            return false;
        }
    }
}
