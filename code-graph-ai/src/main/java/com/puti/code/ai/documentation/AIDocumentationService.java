package com.puti.code.ai.documentation;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * AI文档生成服务
 * 负责调用AI大模型生成说明书内容
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AIDocumentationService {

    @Qualifier("simpleChatClient")
    @Resource
    private ChatClient chatClient;
    @Resource
    private PromptBuilder promptBuilder;
    @Resource
    private BatchDocumentationService batchDocumentationService;

    // 重试配置
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;

    /**
     * 同步生成文档内容
     *
     * @param context 文档生成上下文
     * @return 生成的文档内容
     */
    public String generateDocumentationForLevel(DocumentationGenerationContext context) {
        try {
            log.info("开始为层级 {} 生成文档，入口点: {}", context.getLevel(), context.getEntryPointId());

            // 检查是否需要分批处理（所有层级都支持）
            if (batchDocumentationService.needsBatchProcessing(context)) {
                log.info("第{}层内容过大，使用分批处理模式", context.getLevel());
                String batchContent = batchDocumentationService.generateBatchDocumentation(context);

                if (batchContent == null || batchContent.trim().isEmpty()) {
                    log.error("分批生成文档失败");
                    return null;
                }

                // 后处理分批生成的内容
                String processedContent = postProcessContent(batchContent, context);
                log.info("成功完成第{}层分批文档生成，内容长度: {} 字符", context.getLevel(), processedContent.length());

                return processedContent;
            }

            // 常规单次生成流程
            // 1. 构建提示词（直接使用Spring AI方式）
            Prompt prompt = promptBuilder.buildLevelAnalysisPrompt(context);

            // 2. 调用AI服务生成内容
            String content = callAIServiceWithRetry(prompt);

            if (content == null || content.trim().isEmpty()) {
                log.error("AI服务返回空内容");
                return null;
            }

            // 3. 后处理生成的内容
            String processedContent = postProcessContent(content, context);

            log.info("成功生成层级 {} 的文档，内容长度: {} 字符", context.getLevel(), processedContent.length());

            return processedContent;

        } catch (Exception e) {
            log.error("生成文档时发生错误", e);
            return null;
        }
    }

    /**
     * 带重试机制的AI服务调用（支持Prompt对象）
     */
    private String callAIServiceWithRetry(Prompt prompt) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                log.debug("第 {} 次尝试调用AI服务", attempt);

                String result = callAIService(prompt);

                if (result != null && !result.trim().isEmpty()) {
                    log.debug("AI服务调用成功，第 {} 次尝试", attempt);
                    return result;
                }

                log.warn("AI服务返回空内容，第 {} 次尝试", attempt);

            } catch (Exception e) {
                lastException = e;
                log.warn("AI服务调用失败，第 {} 次尝试: {}", attempt, e.getMessage());

                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("AI服务调用失败，已重试 {} 次", MAX_RETRY_ATTEMPTS, lastException);
        return null;
    }

    /**
     * 调用AI服务（使用 Spring AI ChatClient 和 Prompt对象）
     */
    public String callAIService(Prompt prompt) {
        try {
            log.debug("调用 Spring AI ChatClient with Prompt");

            String response = chatClient.prompt(prompt)
                    .advisors(new SimpleLoggerAdvisor())
                    .call()
                    .content();

            log.debug("AI服务响应成功，内容长度: {}", response != null ? response.length() : 0);
            return response;

        } catch (Exception e) {
            log.error("Spring AI ChatClient 调用失败", e);
            throw new RuntimeException("AI服务调用失败", e);
        }
    }

    /**
     * 后处理生成的内容
     */
    private String postProcessContent(String content, DocumentationGenerationContext context) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        try {
            // 1. 清理多余的空行
            String processed = content.replaceAll("\n{3,}", "\n\n");

            // 2. 确保标题格式正确
            processed = processed.replaceAll("(?m)^#{1,6}\\s*(.+)$", "# $1");

            // 3. 添加生成信息

            return "# " + getDocumentTitle(context) + "\n\n" +
                    "> 本文档由AI自动生成，生成时间: " + LocalDateTime.now() + "\n\n" +
                    processed;

        } catch (Exception e) {
            log.warn("后处理内容时发生错误，返回原始内容", e);
            return content;
        }
    }

    /**
     * 获取文档标题
     */
    private String getDocumentTitle(DocumentationGenerationContext context) {
        return String.format("第%d层级说明书 - %s", context.getLevel(), context.getEntryPointId());
    }
}
