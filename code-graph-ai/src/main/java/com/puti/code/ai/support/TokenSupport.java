package com.puti.code.ai.support;

import com.puti.code.base.model.MethodInfo;

import java.util.List;

public class TokenSupport {
    /**
     * 估算内容大小
     */
    public static int estimateContentSize(List<MethodInfo> methods) {
        return methods.stream()
                .mapToInt(method -> {
                    int size = 0;
                    if (method.getSimpleIdentifier() != null) {
                        size += method.getSimpleIdentifier().length();
                    }
                    if (method.getDescription() != null) {
                        size += method.getDescription().length();
                    }
                    if (method.getContent() != null) {
                        size += method.getContent().length();
                    }
                    return size;
                })
                .sum();
    }
}
