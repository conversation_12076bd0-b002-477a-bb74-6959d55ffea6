## 第3层分析重点

### 全面系统分析
- **完整调用链**: 分析从入口点到底层实现的完整调用路径
- **系统边界**: 识别系统的边界和外部依赖
- **端到端流程**: 理解完整的端到端业务处理流程
- **全局架构**: 从全局视角分析系统架构和设计

### 深度技术分析
- **底层实现**: 深入分析底层的技术实现细节
- **技术栈全貌**: 全面分析使用的技术栈和工具链
- **集成点分析**: 分析与外部系统和服务的集成点
- **数据持久化**: 深入分析数据存储和持久化策略

### 架构和设计评估
- **架构模式**: 全面分析应用的架构模式和设计原则
- **系统质量**: 从多个维度评估系统的质量属性
- **技术债务**: 识别技术债务和潜在的架构问题
- **演进能力**: 评估系统的演进和适应能力

### 性能和扩展性
- **性能全貌**: 全面分析系统的性能特征和瓶颈
- **扩展策略**: 分析水平和垂直扩展的策略和限制
- **资源利用**: 分析CPU、内存、IO等资源的利用情况
- **容量规划**: 提供容量规划和性能优化建议

### 运维和监控
- **可观测性**: 分析系统的日志、监控和追踪能力
- **故障处理**: 分析故障检测、隔离和恢复机制
- **运维友好**: 评估系统的运维友好性和自动化程度
- **安全考虑**: 分析安全相关的设计和实现

### 业务价值和技术价值
- **业务对齐**: 分析技术实现与业务需求的对齐程度
- **投入产出**: 评估技术投入与业务价值的比例
- **竞争优势**: 识别技术实现带来的竞争优势
- **创新点**: 发现技术创新和亮点

### 改进和演进建议
- **短期优化**: 提供可以快速实施的改进建议
- **中期重构**: 建议中期的重构和优化方向
- **长期演进**: 提供长期的技术演进路线图
- **风险评估**: 识别技术风险和应对策略

### 输出要求
- **全面性**: 提供全面、深入的系统分析
- **战略高度**: 从战略角度分析技术决策
- **实操指导**: 提供具体的实施指导和建议
- **前瞻性**: 考虑未来的技术发展和业务需求
